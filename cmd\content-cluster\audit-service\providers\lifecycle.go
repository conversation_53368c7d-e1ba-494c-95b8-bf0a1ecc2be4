package providers

import (
	"context"
	"pxpat-backend/cmd"
	"pxpat-backend/internal/content-cluster/audit-service/types"
	publisher "pxpat-backend/internal/content-cluster/audit-service/messaging/publisher"
	"pxpat-backend/pkg/consul"

	"github.com/gin-gonic/gin"
	"github.com/rs/zerolog/log"
	"go.uber.org/fx"
)

// ProvideLifecycleManager 提供应用生命周期管理
func ProvideLifecycleManager(
	lc fx.Lifecycle,
	cfg *types.Config,
	consulManager *consul.Manager,
	mqPublisher *publisher.Publisher,
	ginEngine *gin.Engine,
) {
	lc.Append(fx.Hook{
		OnStart: func(ctx context.Context) error {
			// 启动Consul管理器
			if err := consulManager.Start(ctx); err != nil {
				log.Fatal().Err(err).Msg("Failed to start Consul manager")
				return err
			}
			log.Info().Msg("Consul manager started successfully")

			// 启动HTTP服务器（在goroutine中）
			go func() {
				log.Info().Int("port", cfg.Server.Port).Msg("Starting audit service server")
				cmd.GraceStartAndClose(cfg.Server, ginEngine)
			}()

			return nil
		},
		OnStop: func(ctx context.Context) error {
			// 停止Consul管理器
			if err := consulManager.Stop(); err != nil {
				log.Error().Err(err).Msg("Failed to stop Consul manager")
			} else {
				log.Info().Msg("Consul manager stopped successfully")
			}

			// 关闭MQ发布器
			if mqPublisher != nil {
				if err := mqPublisher.Close(); err != nil {
					log.Error().Err(err).Msg("Failed to close MQ publisher")
				} else {
					log.Info().Msg("MQ publisher closed successfully")
				}
			}

			return nil
		},
	})
	
	log.Info().Msg("Lifecycle manager initialized successfully")
}
