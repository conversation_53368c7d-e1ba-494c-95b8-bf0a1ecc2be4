# Content Management Service API 接口文档

## 概述

本文档描述了内容管理服务(Content Management Service)的API接口，已导出为OpenAPI 3.0格式的JSON文件：`content-management-openapi.json`

## 服务信息

- **服务名称**: Content Management Service API
- **版本**: 1.0.0
- **基础路径**: `/api/v1`
- **认证方式**: Bearer Token (JWT)

## API 分组

### 1. 系统管理 (System Management)

#### 健康检查
- **GET** `/api/v1/health` - 检查服务健康状态
  - 无需认证
  - 返回服务状态信息

### 2. 内容管理 (Content Management)

#### 内容列表和查询
- **GET** `/api/v1/management/contents` - 获取基础内容列表
  - 支持多种过滤条件：内容类型、状态、用户、分类、标签
  - 支持分页和排序
  - 可选认证

#### 内容详情操作
- **GET** `/api/v1/management/contents/{content_ksuid}` - 获取内容详情
  - 需要认证
  - 返回带详细信息的内容数据

- **PUT** `/api/v1/management/contents/{content_ksuid}/status` - 更新内容状态
  - 需要认证和权限验证
  - 支持状态：draft, published, archived, deleted

- **DELETE** `/api/v1/management/contents/{content_ksuid}` - 删除内容
  - 需要认证和权限验证

#### 用户内容管理
- **GET** `/api/v1/management/users/{user_ksuid}/contents` - 获取用户的所有内容
  - 需要认证
  - 支持分页

#### 批量操作
- **PUT** `/api/v1/management/contents/batch/status` - 批量更新内容状态
  - 需要认证和批量操作权限
  - 返回操作结果统计

- **POST** `/api/v1/management/contents/batch/delete` - 批量删除内容
  - 需要认证和批量操作权限
  - 返回操作结果统计

### 3. 统计分析 (Statistics & Analytics)

#### 概览统计
- **GET** `/api/v1/management/stats/overview` - 获取总体统计概览
  - 需要认证
  - 返回内容总数、观看数、点赞数等统计信息

## 数据模型

### 核心数据结构

#### GlobalResponse
全局响应格式，包含：
- `code`: 响应码
- `msg`: 响应消息
- `data`: 响应数据

#### ContentResponse
内容响应数据，包含：
- `content_ksuid`: 内容唯一标识
- `content_type`: 内容类型 (video, novel, music)
- `title`: 标题
- `description`: 描述
- `user_ksuid`: 用户标识
- `status`: 状态 (draft, published, archived, deleted)
- 统计数据：观看数、点赞数、评论数、收藏数
- 分类和标签信息
- 时间戳信息

#### ContentListResponse
内容列表响应，包含：
- `contents`: 内容数组
- `total`: 总数
- `page`: 当前页
- `limit`: 每页数量
- `total_pages`: 总页数

#### BatchOperationResponse
批量操作响应，包含：
- `success_count`: 成功数量
- `failure_count`: 失败数量
- `failures`: 失败详情数组
- `total_count`: 总数量
- `message`: 操作消息

#### ContentOverviewResponse
内容概览统计，包含：
- 各状态内容数量统计
- 总观看数、点赞数、评论数、收藏数
- 平均数据统计

## 权限和认证

### 认证方式
- 使用JWT Bearer Token认证
- 在请求头中添加：`Authorization: Bearer <token>`

### 权限级别
1. **无需认证**: 健康检查、公开内容列表
2. **基础认证**: 内容详情查看、用户内容管理
3. **内容权限**: 内容状态更新、删除（需要所有权或管理权限）
4. **批量操作权限**: 批量更新、删除操作
5. **统计权限**: 查看统计分析数据

## 错误处理

### HTTP状态码
- `200`: 成功
- `400`: 参数错误
- `401`: 未认证
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

### 错误响应格式
所有错误都使用统一的GlobalResponse格式返回，包含错误码和错误消息。

## 使用说明

1. **导入OpenAPI文档**: 将`content-management-openapi.json`文件导入到API测试工具（如Postman、Swagger UI等）
2. **配置认证**: 在工具中配置Bearer Token认证
3. **测试接口**: 按照文档中的参数要求测试各个接口

## 文件位置

- **OpenAPI规范文件**: `content-management-openapi.json`
- **API摘要文档**: `content-management-api-summary.md`

## 注意事项

1. 所有时间字段使用ISO 8601格式
2. KSUID字段为字符串类型的唯一标识符
3. 分页参数：page从1开始，limit默认为20
4. 批量操作有频率限制，建议合理控制批量大小
5. 统计数据可能有缓存，更新可能有延迟

## 技术栈

- **框架**: Gin (Go)
- **认证**: JWT
- **限流**: Redis
- **追踪**: OpenTelemetry
- **文档**: Swagger/OpenAPI 3.0
