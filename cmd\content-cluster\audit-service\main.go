package main

import (
	// 基础设施 providers
	"pxpat-backend/cmd/content-cluster/audit-service/providers"

	// 业务层 providers
	externalHandler "pxpat-backend/internal/content-cluster/audit-service/external/handler"
	externalService "pxpat-backend/internal/content-cluster/audit-service/external/service"
	serviceHandler "pxpat-backend/internal/content-cluster/audit-service/intra/handler"
	serviceService "pxpat-backend/internal/content-cluster/audit-service/intra/service"
	"pxpat-backend/internal/content-cluster/audit-service/repository"
	"pxpat-backend/internal/content-cluster/audit-service/routes"

	"go.uber.org/fx"
)



func main() {
	app := fx.New(
		// 基础设施层 providers
		fx.Provide(
			providers.ProvideConfig,
			providers.ProvideLogger,
			providers.ProvideDatabase,
			providers.ProvideJWTManager,
			providers.ProvideConsulManager,
			providers.ProvideHealthHandler,
		),

		// 客户端层 providers
		fx.Provide(
			providers.ProvideContentServiceClient,
			providers.ProvideUserServiceClient,
			providers.ProvideTokenServiceClient,
			providers.ProvideFinanceServiceClient,
		),

		// 消息队列 providers
		fx.Provide(
			providers.ProvideMQPublisher,
		),

		// 业务层 providers
		fx.Provide(
			repository.ProvideAuditTasksRepository,
			externalService.ProvideExternalAuditTasksService,
			serviceService.ProvideInternalAuditTasksService,
			externalHandler.ProvideAuditTasksHandler,
			serviceHandler.ProvideServiceAuditHandler,
		),

		// 路由层 providers
		fx.Provide(
			routes.ProvideGinEngine,
		),

		// 生命周期管理
		fx.Invoke(
			providers.ProvideLifecycleManager,
		),
	)

	// 运行应用
	app.Run()
}
